<div class="batches-container">
    <!-- Header Section -->
    <div class="action-container">
        <div class="search-container">
            <cax-autoComplete
                [(ngModel)]="selectedItem"
                [suggestions]="filteredItems"
                (completeMethod)="search($event)"
                field="title"
                [showClear]="true"
                [isloading]="isLoading"
                [style]="{ width: '100%' }"
                [placeholder]="'Search Batch...'"
                [group]="true"
                (onSelect)="onItemSelect($event)">
                <ng-template let-group caxTemplate="group">
                    <div class="autocomplete-group-item">
                        <span>{{ group.label }}</span>
                    </div>
                </ng-template>
                <ng-template let-item caxTemplate="item">
                    <div class="autocomplete-item">
                        <div class="item-list">
                            <div class="item-title">
                                <i class="cax cax-history"></i>
                                <span>{{ item.title }}</span>
                            </div>
                        </div>
                        <span class="batchId" *ngIf="item.batchId">
                            {{ item.batchId }}
                        </span>
                    </div>
                </ng-template>
            </cax-autoComplete>
        </div>
        
        <!-- Filter Section -->
        <div class="filter-container">
            <cax-dropdown
                *ngFor="let filter of filterOptions"
                [options]="getFilterValues(filter)"
                [(ngModel)]="selectedFilters[filter.display_name]"
                [placeholder]="filter.display_name"
                [style]="{ width: '200px', marginRight: '8px' }"
                optionLabel="label"
                optionValue="value"
                [showClear]="true"
                [multiple]="true">
            </cax-dropdown>
        </div>
        
        <div class="button-container">
            <cax-button
                (click)="exportData()"
                severity="secondary"
                leftIcon="cax cax-export"
                label="Export"></cax-button>
            <cax-button
                (click)="openUploadSidebar()"
                leftIcon="cax cax-upload"
                label="Upload New Batch"></cax-button>
        </div>
    </div>

    <!-- Status Tabs -->
    <div class="tabs-container">
        <cax-tabView mode="line" [(activeIndex)]="activeTabIndex" (onTabChange)="onTabChange($event)">
            <cax-tabPanel *ngFor="let tab of statusTabs" [header]="tab.header + ' (' + tab.count + ')'">
                <div class="table-container">
                    <cax-table
                        [columns]="getColumnsForStatus(tab.status)"
                        styleClass="cax-datatable-gridlines"
                        [value]="tab.data"
                        [scrollable]="true"
                        [fontSize]="batchFontSize"
                        [rowSize]="batchRowSize"
                        [(selection)]="selectedBatches"
                        (selectionChange)="onTableSelectionChange($event)"
                        [loading]="tab.loading">
                        
                        <!-- Custom column templates -->
                        <ng-template caxTemplate="header" let-columns>
                            <tr>
                                <th style="width: 3rem">
                                    <cax-tableHeaderCheckbox />
                                </th>
                                <th *ngFor="let col of columns" [style]="col.style">
                                    {{ col.header }}
                                </th>
                            </tr>
                        </ng-template>

                        <ng-template caxTemplate="body" let-rowData let-columns="columns" let-rowIndex="rowIndex">
                            <tr [caxSelectableRow]="rowData" [caxSelectableRowIndex]="rowIndex">
                                <td>
                                    <cax-tableCheckbox [value]="rowData" />
                                </td>
                                <td *ngFor="let col of columns">
                                    <!-- Batch ID Column -->
                                    <div *ngIf="col.field === 'batch_id'" class="batch-id-cell">
                                        <span class="batch-id-link" (click)="openBatchDetails(rowData)">
                                            {{ rowData[col.field] }}
                                        </span>
                                    </div>
                                    
                                    <!-- Name Column -->
                                    <div *ngIf="col.field === 'name'">
                                        {{ rowData[col.field] }}
                                    </div>
                                    
                                    <!-- Description Column -->
                                    <div *ngIf="col.field === 'description'">
                                        {{ rowData[col.field] || '-' }}
                                    </div>
                                    
                                    <!-- Tags Column -->
                                    <div *ngIf="col.field === 'labels'" class="tags-cell">
                                        <cax-chip
                                            *ngFor="let label of rowData.labels"
                                            [label]="getLabelName(label)"
                                            [style]="getLabelStyle(label)"
                                            [size]="'sm'">
                                        </cax-chip>
                                    </div>
                                    
                                    <!-- Created At Column -->
                                    <div *ngIf="col.field === 'created_at'">
                                        {{ formatDate(rowData[col.field]) }}
                                    </div>
                                    
                                    <!-- ETA Column -->
                                    <div *ngIf="col.field === 'eta'">
                                        {{ formatDate(rowData[col.field]) || '-' }}
                                    </div>
                                    
                                    <!-- References Column -->
                                    <div *ngIf="col.field === 'reference_batch_id'" class="references-cell">
                                        <span *ngFor="let ref of rowData.reference_batch_id" class="reference-link">
                                            {{ ref }}
                                        </span>
                                    </div>
                                    
                                    <!-- SC Batch ID Column -->
                                    <div *ngIf="col.field === 'batch_split_group_id'">
                                        {{ rowData[col.field] }}
                                    </div>
                                    
                                    <!-- Accepted Rows Column -->
                                    <div *ngIf="col.field === 'accepted_rows'">
                                        {{ rowData.accepted || 0 }}
                                    </div>
                                    
                                    <!-- Progress Column (for In Progress tab) -->
                                    <div *ngIf="col.field === 'progress'" class="progress-cell">
                                        <div class="progress-bar-container">
                                            <div class="progress-bar">
                                                <div class="progress-fill" [style.width.%]="rowData.progress_percent || 0"></div>
                                            </div>
                                            <span class="progress-text">{{ (rowData.progress_percent || 0) }}%</span>
                                        </div>
                                    </div>
                                    
                                    <!-- Comments Column -->
                                    <div *ngIf="col.field === 'comments'" class="comments-cell">
                                        <cax-button
                                            *ngIf="rowData.has_comments"
                                            icon="cax cax-comment"
                                            severity="secondary"
                                            [size]="'small'"
                                            (click)="openComments(rowData)">
                                        </cax-button>
                                        <span *ngIf="!rowData.has_comments">-</span>
                                    </div>
                                    
                                    <!-- Download Column (for Approved tab) -->
                                    <div *ngIf="col.field === 'download'" class="download-cell">
                                        <cax-button
                                            *ngIf="rowData.download"
                                            icon="cax cax-download"
                                            severity="primary"
                                            [size]="'small'"
                                            (click)="downloadBatch(rowData)">
                                        </cax-button>
                                        <span *ngIf="!rowData.download">-</span>
                                    </div>
                                    
                                    <!-- Actions Column -->
                                    <div *ngIf="col.field === 'actions'" class="actions-cell">
                                        <cax-button
                                            icon="cax cax-delete"
                                            severity="danger"
                                            [size]="'small'"
                                            (click)="deleteBatch(rowData)">
                                        </cax-button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    </cax-table>
                </div>
            </cax-tabPanel>
        </cax-tabView>
    </div>
</div>

<!-- Upload Sidebar -->
<cax-sidebar
    [headerText]="'Upload New Batch'"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '500px' }"
    [(visible)]="uploadSidebarVisible">
    <app-upload-batch-sidebar
        (emitCloseUploadSidebar)="closeUploadSidebar()">
    </app-upload-batch-sidebar>
</cax-sidebar>
