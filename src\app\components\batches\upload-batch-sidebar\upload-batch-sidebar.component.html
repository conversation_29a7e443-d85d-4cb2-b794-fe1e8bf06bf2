<div class="upload-batch-container">
    <cax-inputtext
        [label]="'Batch Name'"
        [placeholder]="'Add batch name'"></cax-inputtext>
        <cax-upload 
        [invalid]="false"
        [maxFileSize]="30"
        componentId="upload1"  
        [allowedFileTypes]="['pdf', 'docs' ,'csv']"/>   
    <cax-upload 
        [inputFiles]="'OutputFiles'" 
        [maxFileSize]="30" />

    <cax-dropdown
        [labelText]="'Process'"
        [style]="{ width: '100%' }"
        [placeholder]="'Select'"
        [options]="[
            { label: 'Enrichment', value: 1 },
            { label: 'Classification', value: 2 },
            { label: 'Validation', value: 3 },
        ]"></cax-dropdown>
    <cax-dropdown
        [labelText]="'Input Format'"
        [style]="{ width: '100%' }"
        [placeholder]="'Select'"
        [options]="[
            { label: 'Enrichment', value: 1 },
            { label: 'Classification', value: 2 },
            { label: 'Validation', value: 3 },
        ]"></cax-dropdown>
    <cax-dropdown
        [labelText]="'Output Format'"
        [style]="{ width: '100%' }"
        [placeholder]="'Select'"
        [options]="[
            { label: 'Enrichment', value: 1 },
            { label: 'Classification', value: 2 },
            { label: 'Validation', value: 3 },
        ]"></cax-dropdown>
    <cax-chips
        [label]="'Tags'"
        [style]="{ width: '100%' }"
        [placeholder]="'Add a tag'"></cax-chips>
    <cax-inputtextarea
        [placeholder]="'Add references'"
        [label]="'References (Optional)'"></cax-inputtextarea>
    <cax-inputtextarea
        [placeholder]="'Add description'"
        [label]="'Description (Optional)'"></cax-inputtextarea>
</div>
