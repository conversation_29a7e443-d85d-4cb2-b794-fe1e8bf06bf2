import { Component } from '@angular/core';
import { ChipsModule } from 'cax-design-system/chips';
import { DropdownModule } from 'cax-design-system/dropdown';
import { InputTextModule } from 'cax-design-system/inputtext';
import { InputTextareaModule } from 'cax-design-system/inputtextarea';
import { UploadModule } from 'cax-design-system/upload';

@Component({
    selector: 'app-upload-batch-sidebar',
    standalone: true,
    imports: [
        DropdownModule,
        InputTextareaModule,
        InputTextModule,
        ChipsModule,
        UploadModule
    ],
    templateUrl: './upload-batch-sidebar.component.html',
    styleUrl: './upload-batch-sidebar.component.scss',
})
export class UploadBatchSidebarComponent {}



